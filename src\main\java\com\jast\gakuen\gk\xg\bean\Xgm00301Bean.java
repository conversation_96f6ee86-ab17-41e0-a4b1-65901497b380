/*
 * Xgm00301Bean.java
 *
 * 著作権  ：Copyright Japan System Techniques Co., Ltd. All Rights Reserved.
 * 会社名  ：日本システム技術株式会社
 *
 */
package com.jast.gakuen.gk.xg.bean;

import javax.enterprise.context.SessionScoped;
import javax.inject.Inject;
import javax.inject.Named;

import com.jast.gakuen.core.common.SystemInfo;
import com.jast.gakuen.core.common.dto.OptionDTO;
import com.jast.gakuen.core.common.service.IOptionService;
import com.jast.gakuen.core.common.util.UtilFaces;
import com.jast.gakuen.core.gk.GkBaseBean;
import com.jast.gakuen.gk.xg.dto.Xgm003CondtionDTO01;
import com.jast.gakuen.gk.xg.service.IXgm003Service;

import lombok.Getter;
import lombok.Setter;

/**
 * 学生納付金通知書出力
 *
 * <AUTHOR> System Techniques Co.,Ltd.
 */
@Named
@SessionScoped
public class Xgm00301Bean extends GkBaseBean {

	@Getter
	@Setter
	protected Xgm003CondtionDTO01 condition = new Xgm003CondtionDTO01();

	/**
	 * 学生納付金通知書ービス
	 */
	@Inject
	protected IXgm003Service xgm003Service;

	/**
	 * オプションサービス
	 */
	@Inject
	protected IOptionService optionService;

	/**
	 * 納付金指定タブ
	 */
	@Inject
	@Getter
	protected Xgm00301T01Bean xgm00301T01Bean;

	/**
	 * 納付金・学生指定タブ
	 */
	@Inject
	@Getter
	protected Xgm00301T02Bean xgm00301T02Bean;

	/**
	 * 学生指定タブ
	 */
	@Inject
	@Getter
	protected Xgm00301T03Bean xgm00301T03Bean;


	/**
	 * 改頁チェックボックス活性フラグ
	 */
	@Getter
	protected boolean changePagesDisabled = true;

	/**
	 * 初期表示処理
	 *
	 * @throws Exception 例外
	 */
	@Override
	protected void doInit() throws Exception {
		// ------------------------------
		// 初期値設定
		// ------------------------------
		// 発行日（hattyuDate）のデフォルト値を現在日付に設定（未設定の場合のみ）
		if (this.condition.getHattyuDate() == null) {
			this.condition.setHattyuDate(SystemInfo.getSystemDate());
		}
		
		// ------------------------------
		// オプション情報を取得
		// ------------------------------
		OptionDTO option = new OptionDTO();
		option.setFormId(UtilFaces.getFormId());
		option.setBaseDto(this.condition);
		this.optionService.get(option);
	}

	/**
	 * オプション情報に保存
	 *
	 * @throws Exception 例外
	 */
	protected void saveOption() throws Exception {
		// オプション情報を保存
		OptionDTO option = new OptionDTO();
		option.setFormId(UtilFaces.getFormId());
		option.setBaseDto(this.condition);
		this.optionService.set(option);
	}
}
